<?php

// اختبار شامل للواجهة الرئيسية ومميزات WIDDX AI
echo "=== اختبار شامل لواجهة WIDDX AI ===\n\n";

// 1. اختبار الواجهة الرئيسية
echo "🌐 اختبار الواجهة الرئيسية...\n";
$mainPageResponse = @file_get_contents('http://localhost:8000');
if ($mainPageResponse !== false) {
    echo "✅ الواجهة الرئيسية تعمل بنجاح\n";
    
    // فحص العناصر المهمة في الواجهة
    $checks = [
        'WIDDX AI' => 'العنوان الرئيسي',
        'widdx-app' => 'حاوي التطبيق الرئيسي',
        'widdx-sidebar' => 'الشريط الجانبي',
        'messages-list' => 'منطقة الرسائل',
        'message-input' => 'حقل إدخال الرسائل',
        'send-button' => 'زر الإرسال',
        'personality-selector' => 'محدد الشخصية',
        'think-mode-toggle' => 'مفتاح وضع التفكير العميق',
        'image-gen-modal' => 'نافذة توليد الصور'
    ];
    
    foreach ($checks as $element => $description) {
        if (strpos($mainPageResponse, $element) !== false) {
            echo "  ✅ $description موجود\n";
        } else {
            echo "  ❌ $description مفقود\n";
        }
    }
} else {
    echo "❌ فشل في الوصول للواجهة الرئيسية\n";
}

echo "\n";

// 2. اختبار API الدردشة
echo "💬 اختبار API الدردشة...\n";
$chatData = [
    'message' => 'مرحبا، كيف حالك؟',
    'session_id' => 'test-session-' . time(),
    'personality' => 'neutral'
];

$chatOptions = [
    'http' => [
        'header' => "Content-type: application/json\r\n",
        'method' => 'POST',
        'content' => json_encode($chatData)
    ]
];

$chatContext = stream_context_create($chatOptions);
$chatResult = @file_get_contents('http://localhost:8000/api/chat', false, $chatContext);

if ($chatResult !== false) {
    $chatResponse = json_decode($chatResult, true);
    if (isset($chatResponse['success']) && $chatResponse['success']) {
        echo "✅ API الدردشة يعمل بنجاح\n";
        echo "  📝 الرد: " . substr($chatResponse['message'], 0, 100) . "...\n";
        
        // فحص البيانات الوصفية
        if (isset($chatResponse['metadata'])) {
            echo "  📊 البيانات الوصفية متوفرة\n";
            if (isset($chatResponse['metadata']['models_used'])) {
                echo "  🤖 النماذج المستخدمة: " . implode(', ', $chatResponse['metadata']['models_used']) . "\n";
            }
        }
    } else {
        echo "❌ API الدردشة فشل: " . ($chatResponse['error'] ?? 'خطأ غير معروف') . "\n";
    }
} else {
    echo "❌ فشل في الاتصال بـ API الدردشة\n";
}

echo "\n";

// 3. اختبار توليد الصور
echo "🎨 اختبار توليد الصور...\n";
$imageData = [
    'prompt' => 'beautiful sunset landscape',
    'style' => 'natural',
    'quality' => 'standard',
    'size' => '1024x1024',
    'provider' => 'gemini'
];

$imageOptions = [
    'http' => [
        'header' => "Content-type: application/json\r\n",
        'method' => 'POST',
        'content' => json_encode($imageData)
    ]
];

$imageContext = stream_context_create($imageOptions);
$imageResult = @file_get_contents('http://localhost:8000/api/features/generate-image', false, $imageContext);

if ($imageResult !== false) {
    $imageResponse = json_decode($imageResult, true);
    if (isset($imageResponse['success']) && $imageResponse['success']) {
        echo "✅ توليد الصور يعمل بنجاح\n";
        echo "  🖼️ عدد الصور المولدة: " . count($imageResponse['images']) . "\n";
        if (!empty($imageResponse['images'])) {
            $firstImage = $imageResponse['images'][0];
            echo "  📁 مسار الصورة: " . $firstImage['local_path'] . "\n";
            echo "  📏 حجم الملف: " . number_format($firstImage['file_size'] / 1024, 2) . " KB\n";
        }
    } else {
        echo "❌ توليد الصور فشل: " . ($imageResponse['error'] ?? 'خطأ غير معروف') . "\n";
    }
} else {
    echo "❌ فشل في الاتصال بـ API توليد الصور\n";
}

echo "\n";

// 4. اختبار المميزات المتقدمة
echo "🚀 اختبار المميزات المتقدمة...\n";

// اختبار البحث المباشر
$searchResult = @file_get_contents('http://localhost:8000/api/features/search', false, stream_context_create([
    'http' => [
        'header' => "Content-type: application/json\r\n",
        'method' => 'POST',
        'content' => json_encode(['query' => 'artificial intelligence'])
    ]
]));

if ($searchResult !== false) {
    $searchResponse = json_decode($searchResult, true);
    if (isset($searchResponse['success']) && $searchResponse['success']) {
        echo "✅ البحث المباشر يعمل\n";
    } else {
        echo "⚠️ البحث المباشر قد لا يعمل بشكل كامل\n";
    }
} else {
    echo "❌ فشل في اختبار البحث المباشر\n";
}

// 5. اختبار الحالة العامة
echo "\n📊 تقرير الحالة العامة:\n";
$healthResult = @file_get_contents('http://localhost:8000/api/health');
if ($healthResult !== false) {
    $healthResponse = json_decode($healthResult, true);
    if (isset($healthResponse['status']) && $healthResponse['status'] === 'ok') {
        echo "✅ النظام يعمل بحالة جيدة\n";
        echo "  🕐 الوقت: " . $healthResponse['timestamp'] . "\n";
        
        if (isset($healthResponse['features'])) {
            echo "  🎯 المميزات المتاحة:\n";
            foreach ($healthResponse['features'] as $feature => $status) {
                $statusIcon = $status ? '✅' : '❌';
                echo "    $statusIcon " . ucfirst(str_replace('_', ' ', $feature)) . "\n";
            }
        }
    } else {
        echo "⚠️ النظام يعمل لكن قد توجد مشاكل\n";
    }
} else {
    echo "❌ فشل في فحص حالة النظام\n";
}

echo "\n=== انتهى الاختبار الشامل ===\n";
echo "📝 ملاحظة: تأكد من أن الخادم يعمل على http://localhost:8000\n";
echo "🌐 يمكنك فتح الواجهة في المتصفح للاختبار اليدوي\n";
