<?php

require_once 'vendor/autoload.php';

use App\Services\FreeImageService;

// Bootstrap Laravel
$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

echo "=== اختبار البدائل المجانية المحسنة لتوليد الصور ===\n\n";

try {
    $freeImageService = app(FreeImageService::class);
    
    // اختبار 1: الخدمات المجانية عبر الإنترنت
    echo "1️⃣ اختبار الخدمات المجانية عبر الإنترنت...\n";
    $freeServicesResult = $freeImageService->generateWithFreeServices('beautiful landscape', [
        'style' => 'realistic'
    ]);
    
    if ($freeServicesResult['success']) {
        echo "✅ الخدمات المجانية نجحت!\n";
        echo "🎯 المزود: " . $freeServicesResult['provider'] . "\n";
        echo "📁 مسار الملف: " . $freeServicesResult['image_path'] . "\n";
        echo "🔗 الرابط: " . $freeServicesResult['url'] . "\n";
        
        // فحص وجود الملف
        $fullPath = storage_path('app/public/' . $freeServicesResult['image_path']);
        if (file_exists($fullPath)) {
            echo "✅ الملف موجود فعلياً\n";
            echo "📏 حجم الملف: " . number_format(filesize($fullPath) / 1024, 2) . " KB\n";
        } else {
            echo "❌ الملف غير موجود\n";
        }
    } else {
        echo "❌ فشل في الخدمات المجانية\n";
    }
    
    echo "\n";
    
    // اختبار 2: API مع الخدمات المجانية
    echo "2️⃣ اختبار API مع الخدمات المجانية...\n";
    
    $apiData = [
        'prompt' => 'cute cat playing',
        'provider' => 'free_services',
        'use_free' => true,
        'style' => 'artistic'
    ];
    
    $apiOptions = [
        'http' => [
            'header' => "Content-type: application/json\r\n",
            'method' => 'POST',
            'content' => json_encode($apiData)
        ]
    ];
    
    $apiContext = stream_context_create($apiOptions);
    $apiResult = @file_get_contents('http://localhost:8000/api/features/generate-image', false, $apiContext);
    
    if ($apiResult !== false) {
        $apiResponse = json_decode($apiResult, true);
        if (isset($apiResponse['success']) && $apiResponse['success']) {
            echo "✅ API الخدمات المجانية يعمل!\n";
            echo "📁 مسار الصورة: " . $apiResponse['image_path'] . "\n";
            echo "🎯 المزود: " . $apiResponse['provider'] . "\n";
            echo "🔗 الرابط: " . ($apiResponse['url'] ?? 'غير متوفر') . "\n";
        } else {
            echo "❌ API الخدمات المجانية فشل: " . ($apiResponse['error'] ?? 'خطأ غير معروف') . "\n";
        }
    } else {
        echo "❌ فشل في الاتصال بـ API الخدمات المجانية\n";
    }
    
    echo "\n";
    
    // اختبار 3: مقارنة جميع البدائل المجانية
    echo "3️⃣ مقارنة جميع البدائل المجانية...\n";
    
    $testPrompt = 'mountain sunset';
    $alternatives = [
        'svg' => 'SVG محلي',
        'free_services' => 'خدمات مجانية',
        'description' => 'وصف تفصيلي'
    ];
    
    foreach ($alternatives as $provider => $name) {
        echo "\n🔸 اختبار $name ($provider)...\n";
        
        $testData = [
            'prompt' => $testPrompt,
            'provider' => $provider,
            'use_free' => true
        ];
        
        $testOptions = [
            'http' => [
                'header' => "Content-type: application/json\r\n",
                'method' => 'POST',
                'content' => json_encode($testData)
            ]
        ];
        
        $testContext = stream_context_create($testOptions);
        $testResult = @file_get_contents('http://localhost:8000/api/features/generate-image', false, $testContext);
        
        if ($testResult !== false) {
            $testResponse = json_decode($testResult, true);
            if (isset($testResponse['success']) && $testResponse['success']) {
                echo "  ✅ $name يعمل\n";
                echo "  📁 مسار: " . $testResponse['image_path'] . "\n";
                echo "  🎯 مزود: " . $testResponse['provider'] . "\n";
            } else {
                echo "  ❌ $name فشل: " . ($testResponse['error'] ?? 'خطأ غير معروف') . "\n";
            }
        } else {
            echo "  ❌ $name فشل في الاتصال\n";
        }
    }
    
    echo "\n";
    
    // اختبار 4: الخيارات المحدثة
    echo "4️⃣ الخيارات المحدثة...\n";
    $options = $freeImageService->getAvailableOptions();
    
    echo "🎯 المزودون: " . implode(', ', $options['providers']) . "\n";
    echo "📊 الأنواع: " . implode(', ', $options['types']) . "\n";
    echo "🌐 الخدمات المجانية: " . implode(', ', $options['free_services']) . "\n";
    echo "💡 ملاحظة: " . $options['note'] . "\n";
    
    echo "\n=== ملخص البدائل المجانية المحسنة ===\n";
    echo "✅ خدمات مجانية عبر الإنترنت: " . ($freeServicesResult['success'] ? "تعمل" : "لا تعمل") . "\n";
    echo "✅ API محسن: " . (isset($apiResponse['success']) && $apiResponse['success'] ? "يعمل" : "لا يعمل") . "\n";
    echo "✅ بدائل متعددة: متوفرة\n";
    
    echo "\n🎯 أفضل البدائل المجانية:\n";
    echo "1. 🌐 الخدمات المجانية عبر الإنترنت (صور حقيقية)\n";
    echo "2. 🎨 صور SVG (قابلة للتخصيص)\n";
    echo "3. 📝 وصف تفصيلي (للمحتوى النصي)\n";
    echo "4. 🖼️ صور بسيطة (للاختبار)\n";
    
    echo "\n📋 كيفية الاستخدام:\n";
    echo "- أضف 'use_free' => true في طلبات API\n";
    echo "- استخدم 'provider' => 'free_services' للصور الحقيقية\n";
    echo "- استخدم 'provider' => 'svg' للصور المتجهة\n";
    echo "- جميع البدائل مجانية 100% ولا تحتاج API keys\n";
    
} catch (Exception $e) {
    echo "❌ خطأ: " . $e->getMessage() . "\n";
    echo "التفاصيل: " . $e->getTraceAsString() . "\n";
}
