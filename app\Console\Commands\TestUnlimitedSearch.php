<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Services\UnlimitedSearchService;
use Illuminate\Support\Facades\Log;

class TestUnlimitedSearch extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'widdx:test-unlimited-search
                            {query? : Search query to test}
                            {--providers=* : Search providers to test}
                            {--max-results=10 : Maximum results per query}
                            {--language=ar : Search language}
                            {--parallel : Enable parallel search}
                            {--bulk : Test bulk search}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Test unlimited search functionality without rate limits';

    private UnlimitedSearchService $unlimitedSearch;

    public function __construct(UnlimitedSearchService $unlimitedSearch)
    {
        parent::__construct();
        $this->unlimitedSearch = $unlimitedSearch;
    }

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('🔍 Testing WIDDX AI Unlimited Search...');
        $this->newLine();

        // Check if unlimited search is enabled
        if (!config('widdx.features.unlimited_search.enabled', true)) {
            $this->error('❌ Unlimited search is disabled in configuration');
            return 1;
        }

        $this->info('✅ Unlimited search is enabled');
        $this->displayConfiguration();

        if ($this->option('bulk')) {
            return $this->testBulkSearch();
        }

        return $this->testSingleSearch();
    }

    /**
     * Display current configuration
     */
    private function displayConfiguration(): void
    {
        $this->info('📋 Current Configuration:');
        $this->table(
            ['Setting', 'Value'],
            [
                ['Rate Limiting Enabled', config('widdx.features.unlimited_search.rate_limiting_enabled', false) ? 'Yes' : 'No'],
                ['Cache Enabled', config('widdx.features.unlimited_search.cache_enabled', true) ? 'Yes' : 'No'],
                ['Cache Duration', config('widdx.features.unlimited_search.cache_duration', 5) . ' minutes'],
                ['Parallel Search', config('widdx.features.unlimited_search.parallel_enabled', true) ? 'Yes' : 'No'],
                ['Max Parallel Requests', config('widdx.features.unlimited_search.max_parallel_requests', 10)],
                ['Timeout', config('widdx.features.unlimited_search.timeout_seconds', 30) . ' seconds'],
            ]
        );
        $this->newLine();
    }

    /**
     * Test single search
     */
    private function testSingleSearch(): int
    {
        $query = $this->argument('query') ?: $this->ask('Enter search query', 'Laravel PHP framework');
        $providers = $this->option('providers') ?: ['duckduckgo'];
        $maxResults = (int) $this->option('max-results');
        $language = $this->option('language');
        $parallel = $this->option('parallel');

        $this->info("🔍 Searching for: '{$query}'");
        $this->info("📡 Providers: " . implode(', ', $providers));
        $this->info("📊 Max Results: {$maxResults}");
        $this->info("🌐 Language: {$language}");
        $this->info("⚡ Parallel: " . ($parallel ? 'Yes' : 'No'));
        $this->newLine();

        $startTime = microtime(true);

        try {
            $result = $this->unlimitedSearch->search($query, [
                'providers' => $providers,
                'max_results' => $maxResults,
                'language' => $language,
                'parallel' => $parallel,
            ]);

            $endTime = microtime(true);
            $duration = round(($endTime - $startTime) * 1000, 2);

            if ($result['success']) {
                $this->info("✅ Search completed successfully in {$duration}ms");
                $this->displaySearchResults($result);
            } else {
                $this->error("❌ Search failed: " . $result['error']);
                return 1;
            }

        } catch (\Exception $e) {
            $this->error("❌ Search error: " . $e->getMessage());
            Log::error('Unlimited search test failed', [
                'query' => $query,
                'error' => $e->getMessage(),
            ]);
            return 1;
        }

        return 0;
    }

    /**
     * Test bulk search
     */
    private function testBulkSearch(): int
    {
        $this->info('🔄 Testing bulk search...');

        $queries = [
            'Laravel framework',
            'PHP programming',
            'Vue.js tutorial',
            'MySQL database',
            'API development',
        ];

        $this->info('📝 Test queries: ' . implode(', ', $queries));
        $this->newLine();

        $startTime = microtime(true);
        $totalResults = 0;

        foreach ($queries as $index => $query) {
            $this->info("🔍 [" . ($index + 1) . "/" . count($queries) . "] Searching: {$query}");

            try {
                $result = $this->unlimitedSearch->search($query, [
                    'providers' => ['duckduckgo'],
                    'max_results' => 5,
                    'language' => 'en',
                ]);

                if ($result['success']) {
                    $resultsCount = count($result['results'] ?? []);
                    $totalResults += $resultsCount;
                    $this->info("   ✅ Found {$resultsCount} results");
                } else {
                    $this->warn("   ⚠️  Failed: " . $result['error']);
                }

            } catch (\Exception $e) {
                $this->error("   ❌ Error: " . $e->getMessage());
            }

            // Small delay to show progress
            usleep(100000); // 0.1 seconds
        }

        $endTime = microtime(true);
        $duration = round(($endTime - $startTime) * 1000, 2);

        $this->newLine();
        $this->info("🎉 Bulk search completed!");
        $this->info("📊 Total queries: " . count($queries));
        $this->info("📈 Total results: {$totalResults}");
        $this->info("⏱️  Total time: {$duration}ms");
        $this->info("⚡ Average time per query: " . round($duration / count($queries), 2) . "ms");

        return 0;
    }

    /**
     * Display search results
     */
    private function displaySearchResults(array $result): void
    {
        $this->newLine();
        $this->info('📊 Search Results:');

        $headers = ['#', 'Title', 'URL', 'Provider'];
        $rows = [];

        foreach ($result['results'] as $index => $item) {
            $rows[] = [
                $index + 1,
                $this->truncateText($item['title'] ?? 'No title', 40),
                $this->truncateText($item['url'] ?? 'No URL', 50),
                $item['provider'] ?? 'Unknown',
            ];
        }

        if (empty($rows)) {
            $this->warn('No results found');
            return;
        }

        $this->table($headers, $rows);

        $this->newLine();
        $this->info('📈 Summary:');
        $this->info("   Total Results: " . count($result['results']));
        $this->info("   Provider: " . ($result['provider'] ?? 'Unknown'));
        $this->info("   Unlimited Mode: " . ($result['unlimited_mode'] ? 'Yes' : 'No'));
        $this->info("   Cache Used: " . (isset($result['cache_hit']) && $result['cache_hit'] ? 'Yes' : 'No'));

        if (isset($result['providers_used'])) {
            $this->info("   Providers Used: " . implode(', ', $result['providers_used']));
        }
    }

    /**
     * Truncate text to specified length
     */
    private function truncateText(string $text, int $length): string
    {
        return strlen($text) > $length ? substr($text, 0, $length - 3) . '...' : $text;
    }
}
