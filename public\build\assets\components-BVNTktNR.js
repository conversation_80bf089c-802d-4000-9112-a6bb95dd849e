var w=(l,t)=>()=>(t||l((t={exports:{}}).exports,t),t.exports);var v=w((y,r)=>{class m{constructor(){this.initializeComponents()}initializeComponents(){this.setupTooltips(),this.setupDropdowns(),this.setupModals(),this.setupNotifications(),this.setupKeyboardShortcuts()}setupTooltips(){document.querySelectorAll("[data-tooltip]").forEach(e=>{const s=this.createTooltip(e.dataset.tooltip);e.addEventListener("mouseenter",i=>{this.showTooltip(s,i.target)}),e.addEventListener("mouseleave",()=>{this.hideTooltip(s)})})}createTooltip(t){const e=document.createElement("div");return e.className="widdx-tooltip",e.textContent=t,e.style.cssText=`
            position: absolute;
            background: var(--widdx-bg-elevated);
            color: var(--widdx-text-primary);
            padding: var(--widdx-space-xs) var(--widdx-space-sm);
            border-radius: var(--widdx-radius-sm);
            font-size: 0.75rem;
            white-space: nowrap;
            z-index: var(--widdx-z-tooltip);
            opacity: 0;
            pointer-events: none;
            transition: opacity var(--widdx-transition-fast);
            box-shadow: var(--widdx-shadow-md);
            border: 1px solid var(--widdx-border-primary);
        `,document.body.appendChild(e),e}showTooltip(t,e){const s=e.getBoundingClientRect();t.style.left=s.left+s.width/2-t.offsetWidth/2+"px",t.style.top=s.top-t.offsetHeight-8+"px",t.style.opacity="1"}hideTooltip(t){t.style.opacity="0",setTimeout(()=>{t.parentNode&&t.parentNode.removeChild(t)},150)}setupDropdowns(){document.querySelectorAll("[data-dropdown]").forEach(e=>{e.addEventListener("click",s=>{s.stopPropagation();const i=e.dataset.dropdown,o=document.getElementById(i);this.toggleDropdown(o,e)})}),document.addEventListener("click",()=>{this.closeAllDropdowns()})}toggleDropdown(t,e){const s=t.classList.contains("widdx-dropdown-open");this.closeAllDropdowns(),s||this.openDropdown(t,e)}openDropdown(t,e){const s=e.getBoundingClientRect();t.style.cssText=`
            position: absolute;
            top: ${s.bottom+8}px;
            left: ${s.left}px;
            min-width: ${s.width}px;
            background: var(--widdx-bg-elevated);
            border: 1px solid var(--widdx-border-primary);
            border-radius: var(--widdx-radius-md);
            box-shadow: var(--widdx-shadow-lg);
            z-index: var(--widdx-z-dropdown);
            opacity: 0;
            transform: translateY(-10px);
            transition: all var(--widdx-transition-fast);
        `,t.classList.add("widdx-dropdown-open"),requestAnimationFrame(()=>{t.style.opacity="1",t.style.transform="translateY(0)"})}closeAllDropdowns(){document.querySelectorAll(".widdx-dropdown-open").forEach(e=>{e.style.opacity="0",e.style.transform="translateY(-10px)",setTimeout(()=>{e.classList.remove("widdx-dropdown-open")},150)})}setupModals(){document.querySelectorAll("[data-modal]").forEach(e=>{e.addEventListener("click",()=>{const s=e.dataset.modal,i=document.getElementById(s);this.openModal(i)})}),document.addEventListener("click",e=>{e.target.classList.contains("widdx-modal-backdrop")&&this.closeModal(e.target.closest(".widdx-modal"))}),document.addEventListener("keydown",e=>{if(e.key==="Escape"){const s=document.querySelector(".widdx-modal.widdx-modal-open");s&&this.closeModal(s)}})}openModal(t){t.classList.add("widdx-modal-open"),document.body.style.overflow="hidden";const e=t.querySelector('button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])');e&&e.focus()}closeModal(t){t.classList.remove("widdx-modal-open"),document.body.style.overflow=""}setupNotifications(){this.notificationContainer=this.createNotificationContainer()}createNotificationContainer(){const t=document.createElement("div");return t.className="widdx-notification-container",t.style.cssText=`
            position: fixed;
            top: var(--widdx-space-lg);
            right: var(--widdx-space-lg);
            z-index: var(--widdx-z-modal);
            display: flex;
            flex-direction: column;
            gap: var(--widdx-space-sm);
            max-width: 400px;
        `,document.body.appendChild(t),t}showNotification(t,e="info",s=5e3){const i=document.createElement("div");i.className=`widdx-notification widdx-notification-${e}`;const o={success:"var(--widdx-success)",error:"var(--widdx-error)",warning:"var(--widdx-warning)",info:"var(--widdx-info)"};return i.style.cssText=`
            background: var(--widdx-bg-elevated);
            border: 1px solid var(--widdx-border-primary);
            border-left: 4px solid ${o[e]};
            border-radius: var(--widdx-radius-md);
            padding: var(--widdx-space-md);
            box-shadow: var(--widdx-shadow-lg);
            transform: translateX(100%);
            transition: transform var(--widdx-transition-normal);
            color: var(--widdx-text-primary);
        `,i.innerHTML=`
            <div class="widdx-flex widdx-items-center widdx-gap-sm">
                <span class="widdx-notification-icon">${this.getNotificationIcon(e)}</span>
                <span class="widdx-notification-message">${t}</span>
                <button class="widdx-notification-close" style="margin-left: auto; background: none; border: none; color: var(--widdx-text-secondary); cursor: pointer;">×</button>
            </div>
        `,this.notificationContainer.appendChild(i),requestAnimationFrame(()=>{i.style.transform="translateX(0)"}),i.querySelector(".widdx-notification-close").addEventListener("click",()=>{this.hideNotification(i)}),s>0&&setTimeout(()=>{this.hideNotification(i)},s),i}hideNotification(t){t.style.transform="translateX(100%)",setTimeout(()=>{t.parentNode&&t.parentNode.removeChild(t)},250)}getNotificationIcon(t){const e={success:"✓",error:"✕",warning:"⚠",info:"ℹ"};return e[t]||e.info}setupKeyboardShortcuts(){this.shortcuts=new Map,document.addEventListener("keydown",t=>{const e=this.getShortcutKey(t),s=this.shortcuts.get(e);s&&(t.preventDefault(),s())})}registerShortcut(t,e){this.shortcuts.set(t,e)}getShortcutKey(t){const e=[];return t.ctrlKey&&e.push("ctrl"),t.altKey&&e.push("alt"),t.shiftKey&&e.push("shift"),t.metaKey&&e.push("meta"),e.push(t.key.toLowerCase()),e.join("+")}debounce(t,e){let s;return function(...o){const a=()=>{clearTimeout(s),t(...o)};clearTimeout(s),s=setTimeout(a,e)}}throttle(t,e){let s;return function(){const i=arguments,o=this;s||(t.apply(o,i),s=!0,setTimeout(()=>s=!1,e))}}}document.addEventListener("DOMContentLoaded",()=>{window.widdxUI=new m});class x{constructor(){this.messagesContainer=document.getElementById("messages-list"),this.typingIndicator=document.getElementById("typing-indicator"),this.scrollBtn=document.getElementById("scroll-to-bottom"),this.featuresPanel=document.getElementById("features-panel"),this.featuresToggle=document.getElementById("features-toggle"),this.messages=[],this.isTyping=!1,this.autoScroll=!0,this.init()}init(){this.setupEventListeners(),this.setupScrollHandling(),this.setupFeatureButtons(),this.setupSuggestions()}setupEventListeners(){var t,e;(t=this.featuresToggle)==null||t.addEventListener("click",()=>{this.toggleFeatures()}),(e=this.scrollBtn)==null||e.addEventListener("click",()=>{this.scrollToBottom(!0)}),document.addEventListener("click",s=>{if(s.target.closest(".widdx-message-action")){const i=s.target.closest(".widdx-message-action").dataset.action,o=s.target.closest(".widdx-message");this.handleMessageAction(i,o)}})}setupScrollHandling(){this.messagesContainer&&this.messagesContainer.addEventListener("scroll",this.debounce(()=>{const{scrollTop:t,scrollHeight:e,clientHeight:s}=this.messagesContainer,i=e-t-s<100;this.autoScroll=i,this.scrollBtn&&this.scrollBtn.classList.toggle("hidden",i)},100))}setupFeatureButtons(){document.querySelectorAll(".widdx-feature-btn").forEach(e=>{e.addEventListener("click",()=>{const s=e.dataset.feature;this.activateFeature(s)})})}setupSuggestions(){document.querySelectorAll(".widdx-suggestion-btn").forEach(e=>{e.addEventListener("click",()=>{const s=e.dataset.suggestion;this.sendSuggestion(s)})})}toggleFeatures(){if(!this.featuresPanel)return;const t=this.featuresPanel.classList.contains("hidden");this.featuresPanel.classList.toggle("hidden",!t),this.featuresToggle&&(this.featuresToggle.classList.toggle("widdx-btn-primary",t),this.featuresToggle.classList.toggle("widdx-btn-secondary",!t))}activateFeature(t){document.querySelectorAll(".widdx-feature-btn").forEach(i=>{i.classList.remove("active")});const e=document.querySelector(`[data-feature="${t}"]`);e&&e.classList.add("active");const s=new CustomEvent("widdx-feature-activated",{detail:{feature:t}});document.dispatchEvent(s)}sendSuggestion(t){const e=document.querySelector(".widdx-welcome-message");e&&(e.style.display="none");const s=new CustomEvent("widdx-send-message",{detail:{message:t}});document.dispatchEvent(s)}addMessage(t,e,s={}){const i="msg_"+Date.now()+"_"+Math.random().toString(36).substr(2,9),o=new Date().toLocaleTimeString("ar-SA",{hour:"2-digit",minute:"2-digit"}),a={id:i,type:t,content:e,timestamp:o,status:s.status||"sent",...s};return this.messages.push(a),this.renderMessage(a),this.autoScroll&&this.scrollToBottom(),i}renderMessage(t){const e=document.createElement("div"),s=this.isRTL(t.content),i=s?"rtl-message":"ltr-message";if(e.className=`widdx-message widdx-message-${t.type} animate-slide-up ${i}`,e.dataset.messageId=t.id,e.dataset.messageType=t.type,e.dir=s?"rtl":"ltr",e.style.direction=s?"rtl":"ltr",e.style.textAlign=s?"right":"left",e.innerHTML=this.getMessageHTML(t),s){const o=e.querySelector(".widdx-message-text");o&&(o.dir="rtl",o.style.direction="rtl",o.style.textAlign="right",o.style.textAlignLast="right",o.style.unicodeBidi="plaintext")}this.messagesContainer&&this.messagesContainer.appendChild(e)}isRTL(t){return console.log("RTL Check: Forcing RTL for testing"),!0}getMessageHTML(t){const{type:e,content:s,timestamp:i,status:o}=t,a=this.isRTL(s),n=a?"rtl":"ltr",d=a?"right":"left",c=a?"rtl-message":"ltr-message",u=a?"widdx-message-text rtl-text":"widdx-message-text";console.log("Rendering message:",{content:s,isRTL:a,direction:n,textAlign:d,messageClass:c,messageTextClass:u});const g=`
            display: flex;
            flex-direction: row;
            direction: ${n} !important;
            unicode-bidi: plaintext;
            text-align: ${d} !important;
            margin-bottom: 1rem;
        `,p=`
            flex: 1;
            direction: ${n} !important;
            text-align: ${d} !important;
            unicode-bidi: plaintext;
        `,h=`
            direction: ${n} !important;
            text-align: ${d} !important;
            unicode-bidi: plaintext !important;
            display: block !important;
            width: 100% !important;
            text-align: ${d} !important;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif !important;
        `;return`
            <div class="widdx-message-container ${c}" style="${g}">
                <div class="widdx-message-avatar">
                    ${e==="user"?this.getUserAvatarHTML():this.getAIAvatarHTML()}
                </div>
                <div class="widdx-message-content" style="${p}">
                    <div class="widdx-message-header">
                        <span class="widdx-message-sender">${e==="user"?"أنت":"WIDDX AI"}</span>
                        <span class="widdx-message-time">${i}</span>
                        ${e==="user"?this.getStatusHTML(o):""}
                    </div>
                    <div class="widdx-message-body" dir="${n}" style="direction: ${n} !important; text-align: ${d} !important; unicode-bidi: plaintext !important;">
                        <div class="${u}" dir="${n}" style="${h}">${s}</div>
                    </div>
                    <div class="widdx-message-actions" style="direction: ltr !important;">
                        ${this.getMessageActionsHTML(e)}
                    </div>
                </div>
            </div>
        `}getUserAvatarHTML(){return`
            <div class="widdx-avatar widdx-avatar-user">
                <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clip-rule="evenodd"></path>
                </svg>
            </div>
        `}getAIAvatarHTML(){return`
            <div class="widdx-avatar widdx-avatar-ai">
                <div class="widdx-gradient-primary rounded-full w-8 h-8 flex items-center justify-center">
                    <span class="text-white font-bold text-sm">W</span>
                </div>
            </div>
        `}getStatusHTML(t){return`<span class="widdx-message-status widdx-message-status-${t}">${{sending:'<svg class="w-3 h-3 animate-spin" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path></svg>',sent:'<svg class="w-3 h-3" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path></svg>',error:'<svg class="w-3 h-3" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path></svg>'}[t]||""}</span>`}getMessageActionsHTML(t){return t==="user"?`
                <button class="widdx-message-action" data-action="edit" data-tooltip="تعديل الرسالة">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                    </svg>
                </button>
            `:`
                <button class="widdx-message-action" data-action="copy" data-tooltip="نسخ الرسالة">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
                    </svg>
                </button>
                <button class="widdx-message-action" data-action="regenerate" data-tooltip="إعادة توليد">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                    </svg>
                </button>
                <button class="widdx-message-action" data-action="like" data-tooltip="إعجاب">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14 10h4.764a2 2 0 011.789 2.894l-3.5 7A2 2 0 0115.263 21h-4.017c-.163 0-.326-.02-.485-.06L7 20m7-10V5a2 2 0 00-2-2h-.095c-.5 0-.905.405-.905.905 0 .714-.211 1.412-.608 2.006L7 11v9m7-10h-2M7 20H5a2 2 0 01-2-2v-6a2 2 0 012-2h2.5"></path>
                    </svg>
                </button>
                <button class="widdx-message-action" data-action="dislike" data-tooltip="عدم إعجاب">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 14H5.236a2 2 0 01-1.789-2.894l3.5-7A2 2 0 018.736 3h4.018c.163 0 .326.02.485.06L17 4m-7 10v5a2 2 0 002 2h.095c.5 0 .905-.405.905-.905 0-.714.211-1.412.608-2.006L17 13V4m-7 10h2m5-10h2a2 2 0 012 2v6a2 2 0 01-2 2h-2.5"></path>
                    </svg>
                </button>
            `}handleMessageAction(t,e){const s=e.dataset.messageId,i=this.messages.find(o=>o.id===s);switch(t){case"copy":this.copyMessage(i);break;case"regenerate":this.regenerateMessage(i);break;case"edit":this.editMessage(i);break;case"like":case"dislike":this.rateMessage(i,t);break}}copyMessage(t){navigator.clipboard&&navigator.clipboard.writeText(t.content).then(()=>{window.widdxUI&&window.widdxUI.showNotification("تم نسخ الرسالة","success",2e3)})}regenerateMessage(t){const e=new CustomEvent("widdx-regenerate-message",{detail:{messageId:t.id}});document.dispatchEvent(e)}editMessage(t){const e=new CustomEvent("widdx-edit-message",{detail:{messageId:t.id,content:t.content}});document.dispatchEvent(e)}rateMessage(t,e){const s=new CustomEvent("widdx-rate-message",{detail:{messageId:t.id,rating:e}});if(document.dispatchEvent(s),window.widdxUI){const i=e==="like"?"شكراً لتقييمك الإيجابي":"شكراً لملاحظاتك";window.widdxUI.showNotification(i,"info",2e3)}}showTyping(){this.isTyping=!0,this.typingIndicator&&this.typingIndicator.classList.remove("hidden"),this.autoScroll&&this.scrollToBottom()}hideTyping(){this.isTyping=!1,this.typingIndicator&&this.typingIndicator.classList.add("hidden")}scrollToBottom(t=!1){this.messagesContainer&&(t||this.autoScroll)&&(this.messagesContainer.scrollTop=this.messagesContainer.scrollHeight)}clearMessages(){this.messages=[],this.messagesContainer&&(this.messagesContainer.innerHTML="")}debounce(t,e){let s;return function(...o){const a=()=>{clearTimeout(s),t(...o)};clearTimeout(s),s=setTimeout(a,e)}}}typeof r<"u"&&r.exports&&(r.exports={WiddxUIComponents:m,WiddxChatArea:x})});export default v();
