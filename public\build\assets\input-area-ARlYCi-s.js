var v=(u,e)=>()=>(e||u((e={exports:{}}).exports,e),e.exports);var I=v((E,h)=>{class f{constructor(){this.messageInput=document.getElementById("message-input"),this.sendButton=document.getElementById("send-button"),this.charCount=document.getElementById("char-count"),this.fileInput=document.getElementById("file-input"),this.imageInput=document.getElementById("image-input"),this.filePreviewArea=document.getElementById("file-preview-area"),this.filePreviewList=document.getElementById("file-preview-list"),this.voiceRecording=document.getElementById("voice-recording"),this.quickCommands=document.getElementById("quick-commands"),this.inputSuggestions=document.getElementById("input-suggestions"),this.notificationContainer=document.getElementById("notification-container"),this.attachedFiles=[],this.isRecording=!1,this.mediaRecorder=null,this.suggestions=[],this.currentSuggestionIndex=-1,this.notificationTimeouts=new Map,this.init()}init(){this.setupEventListeners(),this.setupAutoResize(),this.setupDragAndDrop(),this.setupKeyboardShortcuts(),this.setupVoiceRecognition(),this.loadSuggestions()}setupEventListeners(){var e,t,i,s,a,r,o,l,d,c,g,m,p;(e=this.messageInput)==null||e.addEventListener("input",()=>{this.handleInputChange()}),(t=this.messageInput)==null||t.addEventListener("keydown",n=>{this.handleKeyDown(n)}),(i=this.messageInput)==null||i.addEventListener("focus",()=>{this.showQuickCommands()}),(s=this.messageInput)==null||s.addEventListener("blur",()=>{setTimeout(()=>this.hideQuickCommands(),150)}),(a=this.sendButton)==null||a.addEventListener("click",()=>{this.sendMessage()}),(r=document.getElementById("file-upload-btn"))==null||r.addEventListener("click",()=>{var n;(n=this.fileInput)==null||n.click()}),(o=document.getElementById("image-upload-btn"))==null||o.addEventListener("click",()=>{var n;(n=this.imageInput)==null||n.click()}),(l=document.getElementById("voice-input-btn"))==null||l.addEventListener("click",()=>{this.toggleVoiceRecording()}),(d=document.getElementById("camera-btn"))==null||d.addEventListener("click",()=>{this.openCamera()}),(c=this.fileInput)==null||c.addEventListener("change",n=>{this.handleFileSelect(n.target.files)}),(g=this.imageInput)==null||g.addEventListener("change",n=>{this.handleFileSelect(n.target.files)}),(m=document.getElementById("clear-files"))==null||m.addEventListener("click",()=>{this.clearFiles()}),document.addEventListener("click",n=>{n.target.classList.contains("widdx-quick-cmd")&&this.insertCommand(n.target.dataset.command)}),(p=document.getElementById("stop-recording"))==null||p.addEventListener("click",()=>{this.stopVoiceRecording()})}setupAutoResize(){if(!this.messageInput)return;const e=()=>{this.messageInput.style.height="auto",this.messageInput.style.height=Math.min(this.messageInput.scrollHeight,120)+"px"};this.messageInput.addEventListener("input",e),e()}setupDragAndDrop(){const e=document.querySelector(".widdx-input-container");e&&(["dragenter","dragover","dragleave","drop"].forEach(t=>{e.addEventListener(t,this.preventDefaults,!1)}),["dragenter","dragover"].forEach(t=>{e.addEventListener(t,()=>{e.classList.add("drag-over")},!1)}),["dragleave","drop"].forEach(t=>{e.addEventListener(t,()=>{e.classList.remove("drag-over")},!1)}),e.addEventListener("drop",t=>{const i=t.dataTransfer.files;this.handleFileSelect(i)},!1))}setupKeyboardShortcuts(){document.addEventListener("keydown",e=>{(e.ctrlKey||e.metaKey)&&e.key==="Enter"&&(e.preventDefault(),this.sendMessage()),e.key==="Escape"&&this.messageInput===document.activeElement&&this.clearInput(),this.suggestions.length>0&&!this.inputSuggestions.classList.contains("hidden")&&(e.key==="ArrowDown"?(e.preventDefault(),this.navigateSuggestions(1)):e.key==="ArrowUp"?(e.preventDefault(),this.navigateSuggestions(-1)):e.key==="Tab"&&this.currentSuggestionIndex>=0&&(e.preventDefault(),this.applySuggestion(this.suggestions[this.currentSuggestionIndex])))})}setupVoiceRecognition(){if("webkitSpeechRecognition"in window||"SpeechRecognition"in window){const e=window.SpeechRecognition||window.webkitSpeechRecognition;this.speechRecognition=new e,this.speechRecognition.continuous=!0,this.speechRecognition.interimResults=!0,this.speechRecognition.lang="ar-SA",this.speechRecognition.onresult=t=>{let i="";for(let s=t.resultIndex;s<t.results.length;s++)t.results[s].isFinal&&(i+=t.results[s][0].transcript);i&&(this.messageInput.value+=i,this.handleInputChange())},this.speechRecognition.onerror=t=>{console.error("Speech recognition error:",t.error),this.stopVoiceRecording()}}}loadSuggestions(){this.commonSuggestions=["ابحث عن","ولد صورة","اشرح لي","ما هو","كيف يمكنني","أريد أن أعرف","ساعدني في","قم بتحليل"]}handleInputChange(){var i;const e=((i=this.messageInput)==null?void 0:i.value)||"",t=e.length;if(this.charCount){this.charCount.textContent=t;const s=this.charCount.parentElement;s.classList.toggle("warning",t>8e3),s.classList.toggle("error",t>9500)}this.sendButton&&(this.sendButton.disabled=t===0&&this.attachedFiles.length===0),this.showSuggestions(e),this.saveDraft(e)}handleKeyDown(e){e.key==="Enter"&&!e.shiftKey&&!e.ctrlKey&&(e.preventDefault(),this.currentSuggestionIndex>=0?this.applySuggestion(this.suggestions[this.currentSuggestionIndex]):this.sendMessage()),e.key==="Tab"&&this.messageInput.value.startsWith("/")&&(e.preventDefault(),this.completeCommand())}showSuggestions(e){var i;if(!e||e.length<2){this.hideSuggestions();return}const t=this.commonSuggestions.filter(s=>s.includes(e)||e.includes(s.substring(0,3)));if(t.length===0){this.hideSuggestions();return}this.suggestions=t.slice(0,5),this.renderSuggestions(),(i=this.inputSuggestions)==null||i.classList.remove("hidden")}renderSuggestions(){var t;const e=(t=this.inputSuggestions)==null?void 0:t.querySelector(".widdx-suggestions-list");e&&(e.innerHTML=this.suggestions.map((i,s)=>`
            <div class="widdx-suggestion-item ${s===this.currentSuggestionIndex?"active":""}" 
                 data-index="${s}">
                ${i}
            </div>
        `).join(""),e.querySelectorAll(".widdx-suggestion-item").forEach((i,s)=>{i.addEventListener("click",()=>{this.applySuggestion(this.suggestions[s])})}))}navigateSuggestions(e){this.currentSuggestionIndex=Math.max(-1,Math.min(this.suggestions.length-1,this.currentSuggestionIndex+e)),this.renderSuggestions()}applySuggestion(e){this.messageInput&&(this.messageInput.value=e,this.messageInput.focus(),this.handleInputChange()),this.hideSuggestions()}hideSuggestions(){var e;(e=this.inputSuggestions)==null||e.classList.add("hidden"),this.currentSuggestionIndex=-1,this.suggestions=[]}showQuickCommands(){var e;(e=this.quickCommands)==null||e.classList.remove("hidden")}hideQuickCommands(){var e;(e=this.quickCommands)==null||e.classList.add("hidden")}insertCommand(e){this.messageInput&&(this.messageInput.value=e+" ",this.messageInput.focus(),this.handleInputChange())}completeCommand(){var s;const e=((s=this.messageInput)==null?void 0:s.value)||"",i=["/search","/image","/think","/help","/analyze","/translate"].find(a=>a.startsWith(e));i&&this.messageInput&&(this.messageInput.value=i+" ",this.handleInputChange())}handleFileSelect(e){Array.from(e).forEach(t=>{this.attachedFiles.length<10&&this.attachedFiles.push(t)}),this.renderFilePreview(),this.handleInputChange()}renderFilePreview(){var e,t;if(this.attachedFiles.length===0){(e=this.filePreviewArea)==null||e.classList.add("hidden");return}(t=this.filePreviewArea)==null||t.classList.remove("hidden"),this.filePreviewList&&(this.filePreviewList.innerHTML=this.attachedFiles.map((i,s)=>`
                <div class="widdx-file-preview-item">
                    <div class="file-icon">${this.getFileIcon(i)}</div>
                    <div class="file-info">
                        <div class="file-name">${i.name}</div>
                        <div class="file-size">${this.formatFileSize(i.size)}</div>
                    </div>
                    <button class="widdx-btn widdx-btn-ghost p-1" onclick="widdxInputArea.removeFile(${s})">
                        <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                </div>
            `).join(""))}removeFile(e){this.attachedFiles.splice(e,1),this.renderFilePreview(),this.handleInputChange()}clearFiles(){this.attachedFiles=[],this.renderFilePreview(),this.handleInputChange()}getFileIcon(e){const t=e.type.split("/")[0];return{image:"🖼️",video:"🎥",audio:"🎵",text:"📄",application:"📎"}[t]||"📎"}formatFileSize(e){if(e===0)return"0 Bytes";const t=1024,i=["Bytes","KB","MB","GB"],s=Math.floor(Math.log(e)/Math.log(t));return parseFloat((e/Math.pow(t,s)).toFixed(2))+" "+i[s]}toggleVoiceRecording(){this.isRecording?this.stopVoiceRecording():this.startVoiceRecording()}startVoiceRecording(){var t;if(!this.speechRecognition){this.showNotification("error","Voice recording is not supported in this browser");return}this.isRecording=!0,(t=this.voiceRecording)==null||t.classList.remove("hidden"),this.speechRecognition.start();const e=document.getElementById("voice-input-btn");e==null||e.classList.add("active")}stopVoiceRecording(){var t;if(!this.speechRecognition)return;this.isRecording=!1,this.speechRecognition.stop(),(t=this.voiceRecording)==null||t.classList.add("hidden");const e=document.getElementById("voice-input-btn");e==null||e.classList.remove("active")}showNotification(e,t,i=5e3){const s=`${e}-message-template`,a=document.getElementById(s);if(!a)return console.error(`Template not found: ${s}`),null;const r=a.content.cloneNode(!0),o=r.querySelector(".widdx-alert");o.querySelector("span").textContent=t,this.notificationContainer.appendChild(r);const d=setTimeout(()=>{o.classList.add("widdx-alert-exit"),o.addEventListener("animationend",()=>{o.remove(),this.notificationTimeouts.delete(d)},{once:!0})},i);return this.notificationTimeouts.set(d,o),o.addEventListener("click",c=>{c.target.closest(".widdx-alert-close")&&(clearTimeout(d),o.classList.add("widdx-alert-exit"),o.addEventListener("animationend",()=>{o.remove(),this.notificationTimeouts.delete(d)},{once:!0}))}),o}clearAllNotifications(){this.notificationTimeouts.forEach((e,t)=>{clearTimeout(t),e.remove()}),this.notificationTimeouts.clear()}preventDefaults(e){e.preventDefault(),e.stopPropagation()}}document.addEventListener("DOMContentLoaded",()=>{window.widdxInputArea=new f,window.widdxInputArea.loadDraft()});typeof h<"u"&&h.exports&&(h.exports=f)});export default I();
