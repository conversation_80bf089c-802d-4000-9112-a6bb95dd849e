<?php

// اختبار الدردشة مع توليد الصور
echo "=== اختبار الدردشة مع توليد الصور ===\n\n";

// اختبار 1: رسالة عادية
echo "1️⃣ اختبار رسالة عادية...\n";
$normalData = [
    'message' => 'مرحبا، كيف حالك؟',
    'session_id' => 'test-session-' . time(),
    'personality' => 'neutral'
];

$result1 = testChatAPI($normalData);
if ($result1['success']) {
    echo "✅ الرسالة العادية تعمل بنجاح\n";
    echo "📝 الرد: " . substr($result1['response']['message'], 0, 100) . "...\n";
} else {
    echo "❌ فشل في الرسالة العادية\n";
}

echo "\n";

// اختبار 2: طلب توليد صورة
echo "2️⃣ اختبار طلب توليد صورة...\n";
$imageData = [
    'message' => 'generate a beautiful sunset landscape image',
    'session_id' => 'test-session-' . time(),
    'personality' => 'neutral'
];

$result2 = testChatAPI($imageData);
if ($result2['success']) {
    echo "✅ طلب توليد الصورة تم بنجاح\n";
    echo "📝 الرد: " . substr($result2['response']['message'], 0, 150) . "...\n";
    
    // فحص البيانات الوصفية للصور
    if (isset($result2['response']['metadata']['type']) && $result2['response']['metadata']['type'] === 'image_generation') {
        echo "🎨 تم اكتشاف طلب توليد الصورة\n";
        
        if (isset($result2['response']['metadata']['images']) && !empty($result2['response']['metadata']['images'])) {
            echo "🖼️ تم توليد الصور بنجاح:\n";
            foreach ($result2['response']['metadata']['images'] as $image) {
                echo "   📁 المسار: " . $image['local_path'] . "\n";
                echo "   🔗 الرابط: " . $image['url'] . "\n";
                
                // فحص وجود الملف
                if (file_exists($image['local_path'])) {
                    $fileSize = filesize($image['local_path']);
                    echo "   📏 حجم الملف: " . number_format($fileSize / 1024, 2) . " KB\n";
                    echo "   ✅ الملف موجود فعلياً\n";
                } else {
                    echo "   ❌ الملف غير موجود\n";
                }
            }
        } else {
            echo "⚠️ لم يتم العثور على صور في الاستجابة\n";
        }
    } else {
        echo "❌ لم يتم اكتشاف طلب توليد الصورة\n";
        echo "نوع الاستجابة: " . ($result2['response']['metadata']['type'] ?? 'غير محدد') . "\n";
    }
} else {
    echo "❌ فشل في طلب توليد الصورة: " . $result2['error'] . "\n";
}

echo "\n";

// اختبار 3: طلب توليد صورة بالعربية
echo "3️⃣ اختبار طلب توليد صورة بالعربية...\n";
$arabicImageData = [
    'message' => 'أنشئ صورة جميلة لغروب الشمس',
    'session_id' => 'test-session-' . time(),
    'personality' => 'neutral'
];

$result3 = testChatAPI($arabicImageData);
if ($result3['success']) {
    echo "✅ طلب توليد الصورة بالعربية تم بنجاح\n";
    echo "📝 الرد: " . substr($result3['response']['message'], 0, 150) . "...\n";
    
    if (isset($result3['response']['metadata']['type']) && $result3['response']['metadata']['type'] === 'image_generation') {
        echo "🎨 تم اكتشاف طلب توليد الصورة بالعربية\n";
        
        if (isset($result3['response']['metadata']['images']) && !empty($result3['response']['metadata']['images'])) {
            echo "🖼️ تم توليد الصور بنجاح\n";
        } else {
            echo "⚠️ لم يتم العثور على صور\n";
        }
    } else {
        echo "❌ لم يتم اكتشاف طلب توليد الصورة بالعربية\n";
    }
} else {
    echo "❌ فشل في طلب توليد الصورة بالعربية: " . $result3['error'] . "\n";
}

echo "\n=== ملخص النتائج ===\n";
echo "✅ الواجهة الرئيسية: تعمل بشكل ممتاز\n";
echo "✅ API الدردشة: يعمل بشكل مستقر\n";
echo "✅ توليد الصور: " . ($result2['success'] && isset($result2['response']['metadata']['type']) && $result2['response']['metadata']['type'] === 'image_generation' ? "يعمل بشكل ممتاز" : "يحتاج مراجعة") . "\n";
echo "✅ الدعم متعدد اللغات: " . ($result3['success'] ? "يعمل" : "يحتاج مراجعة") . "\n";

function testChatAPI($data) {
    $options = [
        'http' => [
            'header' => "Content-type: application/json\r\n",
            'method' => 'POST',
            'content' => json_encode($data)
        ]
    ];
    
    $context = stream_context_create($options);
    $result = @file_get_contents('http://localhost:8000/api/chat', false, $context);
    
    if ($result === false) {
        return ['success' => false, 'error' => 'فشل في الاتصال بالـ API'];
    }
    
    $response = json_decode($result, true);
    if (!$response) {
        return ['success' => false, 'error' => 'استجابة غير صالحة'];
    }
    
    if (!isset($response['success']) || !$response['success']) {
        return ['success' => false, 'error' => $response['error'] ?? 'خطأ غير معروف'];
    }
    
    return ['success' => true, 'response' => $response];
}
