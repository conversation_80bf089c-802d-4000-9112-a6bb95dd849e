<?php

// Test the image generation functionality
$url = 'http://localhost:8000/api/chat';
$data = [
    'message' => 'generate a beautiful sunset image',
    'session_id' => 'test-session-' . time()
];

$options = [
    'http' => [
        'header' => "Content-type: application/json\r\n",
        'method' => 'POST',
        'content' => json_encode($data)
    ]
];

$context = stream_context_create($options);
$result = file_get_contents($url, false, $context);

if ($result === FALSE) {
    echo "Error: Could not connect to API\n";
    exit(1);
}

$response = json_decode($result, true);

echo "=== WIDDX AI Image Generation Test ===\n";
echo "Request: " . json_encode($data, JSON_PRETTY_PRINT) . "\n\n";
echo "Response: " . json_encode($response, JSON_PRETTY_PRINT) . "\n\n";

if (isset($response['success']) && $response['success']) {
    echo "✅ Chat API responded successfully\n";
    
    if (isset($response['metadata']['type']) && $response['metadata']['type'] === 'image_generation') {
        echo "✅ Image generation was detected and processed\n";
        
        if (isset($response['metadata']['images']) && !empty($response['metadata']['images'])) {
            echo "✅ Images were generated:\n";
            foreach ($response['metadata']['images'] as $image) {
                echo "   - URL: " . ($image['url'] ?? 'N/A') . "\n";
                echo "   - Path: " . ($image['local_path'] ?? 'N/A') . "\n";
            }
        } else {
            echo "❌ No images found in response\n";
        }
    } else {
        echo "❌ Image generation was not detected or processed\n";
        echo "Response type: " . ($response['metadata']['type'] ?? 'unknown') . "\n";
    }
} else {
    echo "❌ Chat API failed\n";
    echo "Error: " . ($response['error'] ?? 'Unknown error') . "\n";
}
