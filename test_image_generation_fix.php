<?php

require_once 'vendor/autoload.php';

use App\Services\ModelMergerService;
use App\Services\ImageGenerationService;
use App\Services\DeepSeekClient;
use App\Services\GeminiClient;
use App\Services\ContextService;
use App\Services\LiveSearchService;
use App\Services\VoiceService;
use App\Services\DeepSearchService;
use App\Services\ThinkModeService;
use App\Services\DocumentAnalysisService;
use App\Services\VisionService;
use App\Services\FreeSearchService;
use App\Services\FreeImageService;
use App\Services\FreeVoiceService;

// Bootstrap Laravel
$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

echo "=== Testing Image Generation Fix ===\n\n";

try {
    // Create ModelMergerService instance
    $modelMerger = app(ModelMergerService::class);
    
    // Test image generation command detection and processing
    $testMessage = "generate a beautiful sunset image";
    $options = [
        'personality_prompt' => 'You are WIDDX, an intelligent AI assistant.',
        'target_language' => 'english',
        'target_language_code' => 'en',
        'language_confidence' => 0.95,
        'max_tokens' => 2000,
        'temperature' => 0.7,
        'think_mode' => false,
    ];
    
    echo "Testing message: '$testMessage'\n";
    echo "Processing...\n\n";
    
    $result = $modelMerger->processMessage($testMessage, [], $options);
    
    echo "=== RESULT ===\n";
    echo "Success: " . ($result['success'] ? 'YES' : 'NO') . "\n";
    
    if (isset($result['success'])) {
        echo "✅ SUCCESS key is present in response\n";
    } else {
        echo "❌ SUCCESS key is missing from response\n";
    }
    
    if ($result['success']) {
        echo "Content: " . substr($result['content'], 0, 200) . "...\n";
        
        if (isset($result['type']) && $result['type'] === 'image_generation') {
            echo "✅ Image generation was detected and processed\n";
            
            if (isset($result['images']) && !empty($result['images'])) {
                echo "✅ Images were generated:\n";
                foreach ($result['images'] as $image) {
                    echo "   - URL: " . ($image['url'] ?? 'N/A') . "\n";
                    echo "   - Path: " . ($image['local_path'] ?? 'N/A') . "\n";
                }
            } else {
                echo "⚠️  No images found in response (might be using free alternative)\n";
            }
        } else {
            echo "❌ Image generation was not detected\n";
            echo "Response type: " . ($result['type'] ?? 'unknown') . "\n";
        }
    } else {
        echo "❌ Processing failed\n";
        echo "Error: " . ($result['error'] ?? 'Unknown error') . "\n";
    }
    
    echo "\n=== FULL RESPONSE STRUCTURE ===\n";
    echo json_encode($result, JSON_PRETTY_PRINT) . "\n";
    
} catch (Exception $e) {
    echo "❌ Exception occurred: " . $e->getMessage() . "\n";
    echo "Trace: " . $e->getTraceAsString() . "\n";
}
