<?php

/**
 * اختبار API البحث غير المحدود
 * Test Unlimited Search API
 */

// تكوين الاختبار
$baseUrl = 'http://127.0.0.1:8000/api';
$testQueries = [
    'Laravel framework',
    'PHP programming',
    'Vue.js tutorial',
];

echo "🔍 اختبار API البحث غير المحدود\n";
echo "Testing Unlimited Search API\n";
echo str_repeat("=", 50) . "\n\n";

// اختبار 1: البحث البسيط
echo "1️⃣ اختبار البحث البسيط (Simple Search Test)\n";
testSimpleSearch($baseUrl, 'Laravel framework');

echo "\n" . str_repeat("-", 30) . "\n\n";

// اختبار 2: البحث المجمع
echo "2️⃣ اختبار البحث المجمع (Bulk Search Test)\n";
testBulkSearch($baseUrl, $testQueries);

echo "\n" . str_repeat("-", 30) . "\n\n";

// اختبار 3: اقتراحات البحث
echo "3️⃣ اختبار اقتراحات البحث (Search Suggestions Test)\n";
testSearchSuggestions($baseUrl, 'Laravel');

echo "\n" . str_repeat("-", 30) . "\n\n";

// اختبار 4: معلومات النظام
echo "4️⃣ اختبار معلومات النظام (System Capabilities Test)\n";
testSystemCapabilities($baseUrl);

echo "\n🎉 انتهى الاختبار (Testing completed)!\n";

/**
 * اختبار البحث البسيط
 */
function testSimpleSearch($baseUrl, $query) {
    $url = $baseUrl . '/unlimited-search';
    $data = [
        'query' => $query,
        'max_results' => 5,
        'language' => 'ar',
        'providers' => ['duckduckgo']
    ];

    echo "🔍 البحث عن: {$query}\n";
    echo "Searching for: {$query}\n";

    $startTime = microtime(true);
    $result = makeApiRequest($url, $data);
    $endTime = microtime(true);
    
    $duration = round(($endTime - $startTime) * 1000, 2);

    if ($result && isset($result['success']) && $result['success']) {
        $resultsCount = count($result['results'] ?? []);
        echo "✅ نجح البحث (Search successful)\n";
        echo "📊 عدد النتائج (Results count): {$resultsCount}\n";
        echo "⏱️ الوقت المستغرق (Duration): {$duration}ms\n";
        echo "🚀 الوضع غير المحدود (Unlimited mode): " . ($result['unlimited_mode'] ? 'Yes' : 'No') . "\n";
        echo "🚫 محدود بالمعدل (Rate limited): " . ($result['rate_limited'] ? 'Yes' : 'No') . "\n";
        
        if (!empty($result['results'])) {
            echo "\n📋 أول 3 نتائج (First 3 results):\n";
            foreach (array_slice($result['results'], 0, 3) as $i => $item) {
                echo "   " . ($i + 1) . ". " . ($item['title'] ?? 'No title') . "\n";
                echo "      🔗 " . ($item['url'] ?? 'No URL') . "\n";
            }
        }
    } else {
        echo "❌ فشل البحث (Search failed)\n";
        if ($result && isset($result['error'])) {
            echo "   خطأ (Error): " . $result['error'] . "\n";
        }
    }
}

/**
 * اختبار البحث المجمع
 */
function testBulkSearch($baseUrl, $queries) {
    $url = $baseUrl . '/unlimited-search/bulk';
    $data = [
        'queries' => $queries,
        'max_results_per_query' => 3,
        'language' => 'ar',
        'providers' => ['duckduckgo']
    ];

    echo "🔄 البحث المجمع عن " . count($queries) . " استعلامات\n";
    echo "Bulk search for " . count($queries) . " queries\n";

    $startTime = microtime(true);
    $result = makeApiRequest($url, $data);
    $endTime = microtime(true);
    
    $duration = round(($endTime - $startTime) * 1000, 2);

    if ($result && isset($result['success']) && $result['success']) {
        echo "✅ نجح البحث المجمع (Bulk search successful)\n";
        echo "📊 إجمالي الاستعلامات (Total queries): " . $result['total_queries'] . "\n";
        echo "📈 إجمالي النتائج (Total results): " . $result['total_results'] . "\n";
        echo "⏱️ الوقت المستغرق (Duration): {$duration}ms\n";
        echo "⚡ متوسط الوقت لكل استعلام (Average per query): " . round($duration / count($queries), 2) . "ms\n";
        
        if (!empty($result['results'])) {
            echo "\n📋 ملخص النتائج (Results summary):\n";
            foreach ($result['results'] as $item) {
                $query = $item['query'];
                $success = $item['result']['success'] ?? false;
                $count = count($item['result']['results'] ?? []);
                echo "   🔍 {$query}: " . ($success ? "✅ {$count} نتائج" : "❌ فشل") . "\n";
            }
        }
    } else {
        echo "❌ فشل البحث المجمع (Bulk search failed)\n";
        if ($result && isset($result['error'])) {
            echo "   خطأ (Error): " . $result['error'] . "\n";
        }
    }
}

/**
 * اختبار اقتراحات البحث
 */
function testSearchSuggestions($baseUrl, $query) {
    $url = $baseUrl . '/unlimited-search/suggestions';
    $data = [
        'query' => $query,
        'language' => 'ar',
        'max_suggestions' => 5
    ];

    echo "💡 الحصول على اقتراحات لـ: {$query}\n";
    echo "Getting suggestions for: {$query}\n";

    $result = makeApiRequest($url, $data);

    if ($result && isset($result['success']) && $result['success']) {
        $suggestionsCount = count($result['suggestions'] ?? []);
        echo "✅ تم الحصول على الاقتراحات (Suggestions retrieved)\n";
        echo "📊 عدد الاقتراحات (Suggestions count): {$suggestionsCount}\n";
        
        if (!empty($result['suggestions'])) {
            echo "\n📋 الاقتراحات (Suggestions):\n";
            foreach ($result['suggestions'] as $i => $suggestion) {
                echo "   " . ($i + 1) . ". {$suggestion}\n";
            }
        }
    } else {
        echo "❌ فشل في الحصول على الاقتراحات (Failed to get suggestions)\n";
        if ($result && isset($result['error'])) {
            echo "   خطأ (Error): " . $result['error'] . "\n";
        }
    }
}

/**
 * اختبار معلومات النظام
 */
function testSystemCapabilities($baseUrl) {
    $url = $baseUrl . '/unlimited-search/capabilities';

    echo "ℹ️ الحصول على معلومات النظام\n";
    echo "Getting system capabilities\n";

    $result = makeApiRequest($url, null, 'GET');

    if ($result) {
        echo "✅ تم الحصول على معلومات النظام (System info retrieved)\n";
        
        if (isset($result['unlimited_requests'])) {
            echo "🚀 الطلبات غير المحدودة (Unlimited requests): " . ($result['unlimited_requests'] ? 'Yes' : 'No') . "\n";
        }
        
        if (isset($result['rate_limiting'])) {
            echo "🚫 تحديد المعدل (Rate limiting): " . ($result['rate_limiting'] ? 'Yes' : 'No') . "\n";
        }
        
        if (isset($result['parallel_search'])) {
            echo "⚡ البحث المتوازي (Parallel search): " . ($result['parallel_search'] ? 'Yes' : 'No') . "\n";
        }
        
        if (isset($result['supported_providers'])) {
            echo "📡 المزودون المدعومون (Supported providers): " . implode(', ', $result['supported_providers']) . "\n";
        }
        
        if (isset($result['system_status'])) {
            echo "\n🔧 حالة النظام (System status):\n";
            foreach ($result['system_status'] as $key => $value) {
                echo "   {$key}: " . ($value ? 'Enabled' : 'Disabled') . "\n";
            }
        }
    } else {
        echo "❌ فشل في الحصول على معلومات النظام (Failed to get system info)\n";
    }
}

/**
 * إجراء طلب API
 */
function makeApiRequest($url, $data = null, $method = 'POST') {
    $ch = curl_init();
    
    curl_setopt_array($ch, [
        CURLOPT_URL => $url,
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_TIMEOUT => 30,
        CURLOPT_HTTPHEADER => [
            'Content-Type: application/json',
            'Accept: application/json',
        ],
    ]);
    
    if ($method === 'POST' && $data) {
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
    }
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    
    curl_close($ch);
    
    if ($error) {
        echo "❌ خطأ في الاتصال (Connection error): {$error}\n";
        return null;
    }
    
    if ($httpCode !== 200) {
        echo "❌ HTTP Error: {$httpCode}\n";
        echo "Response: {$response}\n";
        return null;
    }
    
    return json_decode($response, true);
}
