# WIDDX AI - البحث غير المحدود (Unlimited Search)

## نظرة عامة

تم تطوير نظام البحث غير المحدود في WIDDX AI لإزالة جميع القيود على طلبات البحث، مما يسمح للذكاء الصناعي بالبحث في الإنترنت بدون حدود للطلبات.

## المميزات الجديدة

### 🚀 البحث بدون قيود
- **لا توجد حدود على عدد الطلبات**: يمكن إجراء عدد غير محدود من طلبات البحث
- **لا توجد قيود زمنية**: لا توجد حدود على الطلبات في الدقيقة أو الساعة
- **البحث المتوازي**: إمكانية البحث عبر عدة مصادر في نفس الوقت
- **البحث المجمع**: إمكانية البحث عن عدة استعلامات في طلب واحد

### 🔧 التحسينات التقنية
- **التخزين المؤقت الذكي**: تخزين النتائج لتقليل الطلبات المكررة
- **إزالة النتائج المكررة**: دمج النتائج من مصادر متعددة وإزالة التكرار
- **مراقبة الأداء**: تسجيل مفصل لجميع طلبات البحث
- **معالجة الأخطاء المحسنة**: استمرارية الخدمة حتى في حالة فشل بعض المصادر

## التكوين

### متغيرات البيئة الجديدة

```env
# Unlimited Search Configuration
WIDDX_UNLIMITED_SEARCH_ENABLED=true
WIDDX_SEARCH_RATE_LIMITING_ENABLED=false
WIDDX_SEARCH_MAX_REQUESTS_PER_MINUTE=0
WIDDX_SEARCH_MAX_REQUESTS_PER_HOUR=0
WIDDX_SEARCH_CACHE_ENABLED=true
WIDDX_SEARCH_CACHE_DURATION=5
WIDDX_SEARCH_PARALLEL_ENABLED=true
WIDDX_SEARCH_MAX_PARALLEL_REQUESTS=10
```

### إعدادات التكوين

في ملف `config/widdx.php`:

```php
'unlimited_search' => [
    'enabled' => env('WIDDX_UNLIMITED_SEARCH_ENABLED', true),
    'rate_limiting_enabled' => env('WIDDX_SEARCH_RATE_LIMITING_ENABLED', false),
    'max_requests_per_minute' => env('WIDDX_SEARCH_MAX_REQUESTS_PER_MINUTE', 0),
    'max_requests_per_hour' => env('WIDDX_SEARCH_MAX_REQUESTS_PER_HOUR', 0),
    'cache_enabled' => env('WIDDX_SEARCH_CACHE_ENABLED', true),
    'cache_duration' => env('WIDDX_SEARCH_CACHE_DURATION', 5),
    'parallel_enabled' => env('WIDDX_SEARCH_PARALLEL_ENABLED', true),
    'max_parallel_requests' => env('WIDDX_SEARCH_MAX_PARALLEL_REQUESTS', 10),
    'default_providers' => ['duckduckgo', 'searx'],
    'timeout_seconds' => env('WIDDX_SEARCH_TIMEOUT', 30),
    'max_results_per_provider' => env('WIDDX_SEARCH_MAX_RESULTS_PER_PROVIDER', 20),
    'deduplication_enabled' => env('WIDDX_SEARCH_DEDUPLICATION_ENABLED', true),
],
```

## واجهات برمجة التطبيقات (APIs)

### 1. البحث غير المحدود

```http
POST /api/unlimited-search
Content-Type: application/json

{
    "query": "Laravel PHP framework",
    "providers": ["duckduckgo"],
    "max_results": 20,
    "language": "ar",
    "region": "SA",
    "parallel": true,
    "cache": true
}
```

### 2. البحث المجمع

```http
POST /api/unlimited-search/bulk
Content-Type: application/json

{
    "queries": [
        "Laravel framework",
        "PHP programming",
        "Vue.js tutorial"
    ],
    "providers": ["duckduckgo"],
    "max_results_per_query": 10,
    "language": "ar",
    "region": "SA"
}
```

### 3. اقتراحات البحث

```http
POST /api/unlimited-search/suggestions
Content-Type: application/json

{
    "query": "Laravel",
    "language": "ar",
    "max_suggestions": 10
}
```

### 4. معلومات النظام

```http
GET /api/unlimited-search/capabilities
```

## الاستخدام في الكود

### البحث البسيط

```php
use App\Services\UnlimitedSearchService;

$unlimitedSearch = app(UnlimitedSearchService::class);

$result = $unlimitedSearch->search('Laravel framework', [
    'providers' => ['duckduckgo'],
    'max_results' => 20,
    'language' => 'ar',
    'region' => 'SA',
]);

if ($result['success']) {
    foreach ($result['results'] as $item) {
        echo $item['title'] . ': ' . $item['url'] . "\n";
    }
}
```

### البحث المتوازي

```php
$result = $unlimitedSearch->search('PHP programming', [
    'providers' => ['duckduckgo', 'searx'],
    'max_results' => 30,
    'parallel' => true,
]);
```

## اختبار النظام

### اختبار من سطر الأوامر

```bash
# اختبار بحث بسيط
php artisan widdx:test-unlimited-search "Laravel framework"

# اختبار مع خيارات متقدمة
php artisan widdx:test-unlimited-search "PHP programming" --providers=duckduckgo --max-results=20 --language=ar --parallel

# اختبار البحث المجمع
php artisan widdx:test-unlimited-search --bulk
```

### اختبار من المتصفح

```javascript
// اختبار البحث غير المحدود
fetch('/api/unlimited-search', {
    method: 'POST',
    headers: {
        'Content-Type': 'application/json',
        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
    },
    body: JSON.stringify({
        query: 'Laravel framework',
        max_results: 20,
        language: 'ar'
    })
})
.then(response => response.json())
.then(data => {
    console.log('Search results:', data);
});
```

## المراقبة والتسجيل

### ملفات السجل

- **البحث العام**: `storage/logs/laravel.log`
- **البحث المخصص**: `storage/logs/search.log`

### مراقبة الأداء

```php
// عرض إحصائيات البحث
$capabilities = $unlimitedSearch->getCapabilities();
echo "Unlimited requests: " . ($capabilities['unlimited_requests'] ? 'Yes' : 'No');
echo "Rate limiting: " . ($capabilities['rate_limiting'] ? 'Yes' : 'No');
echo "Parallel search: " . ($capabilities['parallel_search'] ? 'Yes' : 'No');
```

## الأمان والحماية

### الحماية من إساءة الاستخدام

1. **مراقبة الطلبات**: تسجيل جميع طلبات البحث مع IP والوقت
2. **التخزين المؤقت**: تقليل الطلبات المكررة
3. **حدود المهلة الزمنية**: منع الطلبات طويلة المدى
4. **التحقق من صحة البيانات**: فلترة المدخلات الضارة

### إعدادات الأمان

```php
// في ملف config/widdx.php
'security' => [
    'content_filtering' => true,
    'log_user_messages' => true,
    'anonymize_logs' => true,
],
```

## استكشاف الأخطاء

### المشاكل الشائعة

1. **البحث غير المحدود معطل**
   ```bash
   # تحقق من التكوين
   php artisan config:show widdx.features.unlimited_search.enabled
   ```

2. **فشل البحث المتوازي**
   ```bash
   # تحقق من إعدادات cURL
   php -m | grep curl
   ```

3. **مشاكل التخزين المؤقت**
   ```bash
   # مسح التخزين المؤقت
   php artisan cache:clear
   ```

### رسائل الخطأ

- `Unlimited search is currently disabled`: البحث غير المحدود معطل في التكوين
- `Search provider not available`: مزود البحث غير متاح
- `Request timeout`: انتهت مهلة الطلب

## الدعم والمساعدة

للحصول على المساعدة أو الإبلاغ عن مشاكل:

1. تحقق من ملفات السجل في `storage/logs/`
2. استخدم أمر الاختبار: `php artisan widdx:test-unlimited-search`
3. تحقق من التكوين: `php artisan config:show widdx.features.unlimited_search`

---

## ملاحظات مهمة

- ⚠️ **تأكد من تفعيل البحث غير المحدود في ملف `.env`**
- 🔧 **قم بتشغيل `php artisan config:cache` بعد تغيير التكوين**
- 📊 **راقب استخدام الموارد عند تفعيل البحث المتوازي**
- 🛡️ **استخدم التخزين المؤقت لتحسين الأداء**

تم تطوير هذا النظام لضمان حصول WIDDX AI على إمكانيات بحث قوية وغير محدودة مع الحفاظ على الأداء والأمان.
