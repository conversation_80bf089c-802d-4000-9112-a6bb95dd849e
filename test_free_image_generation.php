<?php

require_once 'vendor/autoload.php';

use App\Services\FreeImageService;
use App\Services\GeminiClient;

// Bootstrap Laravel
$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

echo "=== اختبار البدائل المجانية لتوليد الصور ===\n\n";

try {
    $freeImageService = app(FreeImageService::class);
    
    // اختبار 1: توليد وصف تفصيلي للصورة
    echo "1️⃣ اختبار توليد وصف تفصيلي للصورة...\n";
    $descriptionResult = $freeImageService->generateImageDescription('beautiful sunset over mountains', [
        'style' => 'realistic',
        'detail' => 'high'
    ]);
    
    if ($descriptionResult['success']) {
        echo "✅ توليد الوصف نجح!\n";
        echo "📝 الوصف: " . substr($descriptionResult['description'], 0, 200) . "...\n";
        echo "🎨 ASCII Art: " . (!empty($descriptionResult['ascii_art']) ? "متوفر" : "غير متوفر") . "\n";
        echo "📁 مسار الملف: " . $descriptionResult['image_path'] . "\n";
    } else {
        echo "❌ فشل في توليد الوصف\n";
    }
    
    echo "\n";
    
    // اختبار 2: توليد صورة SVG
    echo "2️⃣ اختبار توليد صورة SVG...\n";
    $svgResult = $freeImageService->generateSvgImage('a simple house with a tree', [
        'style' => 'simple'
    ]);
    
    if ($svgResult['success']) {
        echo "✅ توليد SVG نجح!\n";
        echo "📁 مسار الملف: " . $svgResult['image_path'] . "\n";
        echo "📏 نوع الملف: " . $svgResult['metadata']['type'] . "\n";
        
        // فحص وجود الملف
        $fullPath = storage_path('app/public/' . $svgResult['image_path']);
        if (file_exists($fullPath)) {
            echo "✅ الملف موجود فعلياً\n";
            echo "📏 حجم الملف: " . number_format(filesize($fullPath) / 1024, 2) . " KB\n";
        } else {
            echo "❌ الملف غير موجود\n";
        }
    } else {
        echo "❌ فشل في توليد SVG\n";
    }
    
    echo "\n";
    
    // اختبار 3: الخيارات المتاحة
    echo "3️⃣ الخيارات المتاحة...\n";
    $options = $freeImageService->getAvailableOptions();
    
    echo "🎯 المزودون المتاحون: " . implode(', ', $options['providers']) . "\n";
    echo "📊 الأنواع المتاحة: " . implode(', ', $options['types']) . "\n";
    echo "🎨 الأنماط المتاحة: " . implode(', ', $options['styles']) . "\n";
    echo "📏 الأحجام المتاحة: " . implode(', ', $options['sizes']) . "\n";
    echo "💡 ملاحظة: " . $options['note'] . "\n";
    
    echo "\n";
    
    // اختبار 4: اختبار من خلال API
    echo "4️⃣ اختبار API البدائل المجانية...\n";
    
    // اختبار API توليد الصور مع البديل المجاني
    $apiData = [
        'prompt' => 'beautiful garden with flowers',
        'provider' => 'svg',
        'use_free' => true,
        'style' => 'artistic'
    ];
    
    $apiOptions = [
        'http' => [
            'header' => "Content-type: application/json\r\n",
            'method' => 'POST',
            'content' => json_encode($apiData)
        ]
    ];
    
    $apiContext = stream_context_create($apiOptions);
    $apiResult = @file_get_contents('http://localhost:8000/api/features/generate-image', false, $apiContext);
    
    if ($apiResult !== false) {
        $apiResponse = json_decode($apiResult, true);
        if (isset($apiResponse['success']) && $apiResponse['success']) {
            echo "✅ API البدائل المجانية يعمل!\n";
            echo "📁 مسار الصورة: " . $apiResponse['image_path'] . "\n";
            echo "🎯 المزود: " . $apiResponse['provider'] . "\n";
        } else {
            echo "❌ API البدائل المجانية فشل: " . ($apiResponse['error'] ?? 'خطأ غير معروف') . "\n";
        }
    } else {
        echo "❌ فشل في الاتصال بـ API البدائل المجانية\n";
    }
    
    echo "\n=== ملخص البدائل المجانية ===\n";
    echo "✅ توليد وصف تفصيلي: " . ($descriptionResult['success'] ? "يعمل" : "لا يعمل") . "\n";
    echo "✅ توليد صور SVG: " . ($svgResult['success'] ? "يعمل" : "لا يعمل") . "\n";
    echo "✅ API البدائل المجانية: " . (isset($apiResponse['success']) && $apiResponse['success'] ? "يعمل" : "لا يعمل") . "\n";
    
    echo "\n🎯 التوصيات:\n";
    echo "1. استخدم 'provider' => 'svg' للحصول على صور SVG حقيقية\n";
    echo "2. استخدم 'provider' => 'description' للحصول على وصف تفصيلي\n";
    echo "3. أضف 'use_free' => true في طلبات API لاستخدام البدائل المجانية\n";
    echo "4. جميع البدائل المجانية تعمل بدون الحاجة لـ API keys مدفوعة\n";
    
} catch (Exception $e) {
    echo "❌ خطأ: " . $e->getMessage() . "\n";
    echo "التفاصيل: " . $e->getTraceAsString() . "\n";
}
