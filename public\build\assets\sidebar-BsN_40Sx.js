var h=(r,t)=>()=>(t||r((t={exports:{}}).exports,t),t.exports);var l=h((w,d)=>{class c{constructor(){this.sidebar=document.getElementById("widdx-sidebar"),this.overlay=document.getElementById("sidebar-overlay"),this.toggleBtn=document.getElementById("sidebar-toggle"),this.newChatBtn=document.getElementById("new-chat-btn"),this.chatHistory=document.getElementById("chat-history"),this.isCollapsed=localStorage.getItem("widdx-sidebar-collapsed")==="true",this.isMobile=window.innerWidth<1024,this.chatSessions=this.loadChatSessions(),this.init()}init(){this.setupEventListeners(),this.setupKeyboardShortcuts(),this.renderChatHistory(),this.updateSidebarState(),this.setupQuickActions(),window.addEventListener("resize",this.debounce(()=>{this.isMobile=window.innerWidth<1024,this.updateSidebarState()},250))}setupEventListeners(){var t,e,i,o;(t=this.toggleBtn)==null||t.addEventListener("click",()=>{this.toggleSidebar()}),(e=this.overlay)==null||e.addEventListener("click",()=>{this.isMobile&&this.closeSidebar()}),(i=this.newChatBtn)==null||i.addEventListener("click",()=>{this.createNewChat()}),(o=this.chatHistory)==null||o.addEventListener("click",s=>{const a=s.target.closest(".widdx-chat-item");a&&this.selectChat(a.dataset.chatId)}),document.addEventListener("keydown",s=>{s.key==="Escape"&&this.isMobile&&!this.isCollapsed&&this.closeSidebar()})}setupKeyboardShortcuts(){window.widdxUI&&(window.widdxUI.registerShortcut("ctrl+b",()=>this.toggleSidebar()),window.widdxUI.registerShortcut("ctrl+n",()=>this.createNewChat()),window.widdxUI.registerShortcut("ctrl+shift+h",()=>this.showChatHistory()))}setupQuickActions(){document.querySelectorAll(".widdx-quick-action").forEach(e=>{e.addEventListener("click",()=>{const i=e.dataset.action;this.handleQuickAction(i)})})}handleQuickAction(t){const e=new CustomEvent("widdx-quick-action",{detail:{action:t}});if(document.dispatchEvent(e),this.isMobile&&this.closeSidebar(),window.widdxUI){const i={search:"البحث المباشر","image-gen":"توليد الصور",voice:"التحكم الصوتي","think-mode":"وضع التفكير",settings:"الإعدادات",help:"المساعدة"};window.widdxUI.showNotification(`تم تفعيل ${i[t]}`,"info",3e3)}}toggleSidebar(){this.isMobile?this.isCollapsed?this.openSidebar():this.closeSidebar():(this.isCollapsed=!this.isCollapsed,this.updateSidebarState(),this.saveSidebarState())}openSidebar(){var t;this.isCollapsed=!1,this.updateSidebarState(),this.isMobile&&((t=this.overlay)==null||t.classList.add("active"),document.body.style.overflow="hidden")}closeSidebar(){var t;this.isMobile&&(this.isCollapsed=!0,this.updateSidebarState(),(t=this.overlay)==null||t.classList.remove("active"),document.body.style.overflow="")}updateSidebarState(){this.sidebar&&(this.isCollapsed?(this.sidebar.classList.add("collapsed"),this.isMobile&&this.sidebar.classList.remove("open")):(this.sidebar.classList.remove("collapsed"),this.isMobile&&this.sidebar.classList.add("open")),this.updateToggleIcon())}updateToggleIcon(){var e;const t=(e=this.toggleBtn)==null?void 0:e.querySelector("svg");t&&(this.isCollapsed?t.innerHTML='<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 5l7 7-7 7M5 5l7 7-7 7"></path>':t.innerHTML='<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 19l-7-7 7-7m8 14l-7-7 7-7"></path>')}createNewChat(){const t=this.generateChatId(),e={id:t,title:"New Chat",timestamp:new Date().toISOString(),messages:[]};this.chatSessions.unshift(e),this.saveChatSessions(),this.renderChatHistory(),this.selectChat(t);const i=new CustomEvent("widdx-new-chat",{detail:{chatId:t}});document.dispatchEvent(i)}selectChat(t){var o;const e=(o=this.chatHistory)==null?void 0:o.querySelectorAll(".widdx-chat-item");e==null||e.forEach(s=>{s.classList.toggle("active",s.dataset.chatId===t)});const i=new CustomEvent("widdx-select-chat",{detail:{chatId:t}});document.dispatchEvent(i)}renderChatHistory(){if(!this.chatHistory)return;const t=this.chatSessions.map(e=>`
            <div class="widdx-chat-item group" data-chat-id="${e.id}">
                <div class="flex items-center space-x-3 p-3 mx-2 rounded-lg hover:bg-widdx-bg-hover cursor-pointer transition-colors">
                    <div class="w-2 h-2 bg-widdx-text-tertiary rounded-full flex-shrink-0"></div>
                    <div class="flex-1 min-w-0">
                        <p class="text-sm text-widdx-text-primary truncate sidebar-text">${e.title}</p>
                        <p class="text-xs text-widdx-text-tertiary sidebar-text">${this.formatTimestamp(e.timestamp)}</p>
                    </div>
                    <div class="opacity-0 group-hover:opacity-100 transition-opacity">
                        <button class="widdx-chat-options p-1 hover:bg-widdx-bg-elevated rounded"
                                onclick="event.stopPropagation(); widdxSidebar.showChatOptions('${e.id}')">
                            <svg class="w-4 h-4 text-widdx-text-secondary" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M10 6a2 2 0 110-4 2 2 0 010 4zM10 12a2 2 0 110-4 2 2 0 010 4zM10 18a2 2 0 110-4 2 2 0 010 4z"></path>
                            </svg>
                        </button>
                    </div>
                </div>
            </div>
        `).join("");this.chatHistory.innerHTML=t}showChatOptions(t){console.log("Chat options for:",t,[{label:"Rename",action:()=>this.renameChat(t)},{label:"Export",action:()=>this.exportChat(t)},{label:"Delete",action:()=>this.deleteChat(t),danger:!0}])}renameChat(t){const e=this.chatSessions.find(o=>o.id===t);if(!e)return;const i=prompt("New chat name:",e.title);i&&i.trim()&&(e.title=i.trim(),this.saveChatSessions(),this.renderChatHistory())}deleteChat(t){confirm("Are you sure you want to delete this chat?")&&(this.chatSessions=this.chatSessions.filter(e=>e.id!==t),this.saveChatSessions(),this.renderChatHistory())}exportChat(t){const e=this.chatSessions.find(n=>n.id===t);if(!e)return;const i=JSON.stringify(e,null,2),o=new Blob([i],{type:"application/json"}),s=URL.createObjectURL(o),a=document.createElement("a");a.href=s,a.download=`widdx-chat-${e.title}-${new Date().toISOString().split("T")[0]}.json`,a.click(),URL.revokeObjectURL(s)}generateChatId(){return"chat_"+Date.now()+"_"+Math.random().toString(36).substr(2,9)}formatTimestamp(t){const e=new Date(t),o=new Date-e,s=Math.floor(o/6e4),a=Math.floor(o/36e5),n=Math.floor(o/864e5);return s<1?"Now":s<60?`${s}m ago`:a<24?`${a}h ago`:n<7?`${n}d ago`:e.toLocaleDateString("en-US")}loadChatSessions(){try{const t=localStorage.getItem("widdx-chat-sessions");return t?JSON.parse(t):[]}catch(t){return console.error("Error loading chat sessions:",t),[]}}saveChatSessions(){try{localStorage.setItem("widdx-chat-sessions",JSON.stringify(this.chatSessions))}catch(t){console.error("Error saving chat sessions:",t)}}saveSidebarState(){localStorage.setItem("widdx-sidebar-collapsed",this.isCollapsed.toString())}debounce(t,e){let i;return function(...s){const a=()=>{clearTimeout(i),t(...s)};clearTimeout(i),i=setTimeout(a,e)}}}document.addEventListener("DOMContentLoaded",()=>{window.widdxSidebar=new c});typeof d<"u"&&d.exports&&(d.exports=c)});export default l();
