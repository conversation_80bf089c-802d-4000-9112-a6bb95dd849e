<?php

require_once 'vendor/autoload.php';

use App\Services\ModelMergerService;

// Bootstrap Laravel
$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

// Test the detectAndProcessCommands method
$modelMerger = app(ModelMergerService::class);

// Test messages
$testMessages = [
    "Generate a beautiful image",
    "ارسم صورة جميلة",
    "Create an image of a cat",
    "Hello, how are you?",
    "Search for latest news"
];

echo "Testing message detection:\n\n";

foreach ($testMessages as $message) {
    echo "Testing: '$message'\n";
    
    // Use reflection to access private method
    $reflection = new ReflectionClass($modelMerger);
    $method = $reflection->getMethod('detectAndProcessCommands');
    $method->setAccessible(true);
    
    $options = ['think_mode' => false];
    $result = $method->invoke($modelMerger, $message, $options);
    
    if ($result) {
        echo "  -> Detected as: " . $result['type'] . "\n";
        if (isset($result['content'])) {
            echo "  -> Content: " . substr($result['content'], 0, 100) . "...\n";
        }
    } else {
        echo "  -> No special command detected\n";
    }
    echo "\n";
}
